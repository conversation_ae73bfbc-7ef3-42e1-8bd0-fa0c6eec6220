# 健教中心助手 - 安装和使用指南

## 📦 安装步骤

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者点击Chrome菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择本扩展的文件夹（包含manifest.json的文件夹）
   - 点击"选择文件夹"

4. **确认安装**
   - 扩展会出现在扩展列表中
   - 浏览器工具栏会显示扩展图标
   - 安装完成！

### 方法二：打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择扩展根目录
   - 生成.crx文件和.pem密钥文件

2. **安装打包文件**
   - 将生成的.crx文件拖拽到扩展管理页面
   - 点击"添加扩展程序"确认安装

## 🚀 使用方法

### 基本使用流程

1. **访问目标网站**
   ```
   https://phc.cmechina.net
   ```

2. **登录账户**
   - 确保已登录到云课堂平台
   - 扩展只在登录状态下工作

3. **进入课程页面**
   - 访问包含视频的课程页面
   - 扩展会自动检测页面类型

4. **使用扩展功能**
   - 页面右上角会出现控制按钮
   - 点击浏览器工具栏的扩展图标打开控制面板

### 控制面板功能

点击扩展图标打开控制面板，包含以下功能：

#### 🔧 基本设置
- **启用扩展**: 总开关，控制扩展是否工作
- **当前状态**: 显示扩展当前工作状态
- **网站兼容**: 显示当前网站是否兼容

#### 🎥 视频控制
- **自动跳过视频**: 开启后自动跳过所有视频
- **播放速度**: 设置视频播放倍速（1x-16x）
- **跳过当前视频**: 立即跳过正在播放的视频

#### 📝 考试功能
- **自动答题**: 开启后自动处理考试题目
- **重置设置**: 恢复所有设置到默认值

### 页面按钮功能

在兼容页面上，扩展会显示以下按钮：

#### 视频页面
- **跳过视频**: 红色按钮，点击立即跳过当前视频
- 位置：页面右上角

#### 考试页面
- **自动答题**: 蓝色按钮，点击开始自动答题
- 位置：页面右上角

### 状态指示器

页面右下角的状态指示器显示扩展工作状态：

- 🟢 **绿色**: 功能正常工作
- 🟡 **黄色**: 警告或等待状态  
- 🔴 **红色**: 错误或功能不可用

## ⚙️ 功能详解

### 视频播放控制

#### 支持的播放器类型
- **保利威(Polyv)播放器**: 自动检测并使用API控制
- **HTML5视频元素**: 直接控制video标签
- **其他播放器**: 尝试通用方法处理

#### 跳过视频原理
1. 检测播放器类型
2. 设置音量为0（静音）
3. 跳转到视频结尾
4. 触发播放结束事件
5. 查找并点击"完成"按钮

#### 播放速度控制
- 支持1x到16x倍速播放
- 自动保存用户设置
- 兼容不同播放器

### 自动答题功能

#### 题目识别
- 自动识别单选题和多选题
- 支持多种页面布局
- 智能查找题目容器

#### 答题策略
- **单选题**: 选择第一个选项
- **多选题**: 选择前两个选项
- **简单有效**: 适用于练习和测试

#### 提交机制
- 自动查找提交按钮
- 移除新窗口打开属性
- 确保在当前页面提交

### 课程进度跟踪

#### 进度监控
- 监听视频播放进度
- 触发系统进度更新事件
- 确保系统正确记录

#### 完成确认
- 自动处理完成确认对话框
- 智能识别确认按钮
- 自动点击继续按钮

#### 互动内容处理
- 检测页面互动元素
- 自动处理简单互动
- 跳过复杂交互内容

## 🔧 故障排除

### 常见问题及解决方案

#### Q: 扩展图标不显示？
**A: 检查安装状态**
- 确认扩展已正确安装
- 检查扩展是否被禁用
- 重启浏览器后重试

#### Q: 扩展没有激活？
**A: 检查网站兼容性**
- 确保访问的是 `phc.cmechina.net`
- 检查是否已登录账户
- 刷新页面重新加载扩展

#### Q: 播放器检测失败？
**A: 等待页面加载**
- 等待页面完全加载完成
- 某些播放器需要时间初始化
- 尝试手动播放视频后再使用功能

#### Q: 跳过视频不工作？
**A: 检查播放器兼容性**
- 确认播放器类型是否支持
- 查看控制台错误信息
- 尝试手动点击完成按钮

#### Q: 自动答题不准确？
**A: 功能限制说明**
- 自动答题使用简单策略
- 仅适用于练习和测试
- 重要考试建议手动完成

#### Q: 设置无法保存？
**A: 检查存储权限**
- 确认扩展有存储权限
- 清除浏览器缓存后重试
- 重新安装扩展

### 调试方法

#### 查看控制台日志
1. 按F12打开开发者工具
2. 切换到"Console"标签
3. 查找以"[健教中心助手]"开头的日志
4. 根据错误信息排查问题

#### 检查扩展状态
1. 打开扩展管理页面
2. 查看扩展是否启用
3. 检查是否有错误提示
4. 尝试重新加载扩展

#### 测试功能
1. 使用提供的test.html测试页面
2. 验证各项功能是否正常
3. 对比预期行为和实际结果

## 📋 注意事项

### 使用限制
- ✅ 仅在目标网站工作
- ✅ 需要Chrome 88+版本
- ✅ 需要启用JavaScript
- ❌ 不支持其他浏览器
- ❌ 不支持移动设备

### 安全提醒
- 🔒 不收集个人信息
- 🔒 不向外发送数据
- 🔒 仅在本地存储设置
- ⚠️ 请合理使用功能
- ⚠️ 遵守网站使用条款

### 免责声明
- 本扩展仅供学习研究使用
- 用户需遵守相关法律法规
- 开发者不承担使用责任
- 请勿用于违法违规行为

## 📞 技术支持

### 获取帮助
- 📖 查看README.md文档
- 🐛 提交Issue反馈问题
- 💡 提出功能建议
- 📧 联系开发者

### 版本更新
- 定期检查更新
- 关注功能改进
- 及时升级版本
- 反馈使用体验

---

**祝您使用愉快！** 🎉
