/**
 * 健教中心助手 - Content Script
 * 主要功能脚本，处理视频播放器检测、控制和自动化功能
 */

(function() {
    'use strict';

    /**
     * 主扩展类
     */
    class HealthEduAssistant {
        constructor() {
            this.settings = {
                extensionEnabled: true,
                autoSkipVideo: false,
                autoExam: false,
                playbackSpeed: '2'
            };
            
            this.players = {
                polyv: null,
                html5: null,
                detected: false
            };
            
            this.ui = {
                panel: null,
                skipButton: null,
                examButton: null,
                statusIndicator: null
            };
            
            this.isInitialized = false;
            this.currentUrl = window.location.href;
            
            this.init();
        }

        /**
         * 初始化扩展
         */
        async init() {
            console.log('[健教中心助手] 开始初始化...');

            try {
                // 检查网站兼容性
                const compatibility = this.checkSiteCompatibility();
                if (!compatibility.isTargetSite) {
                    console.log('[健教中心助手] 不在目标网站，跳过初始化');
                    return;
                }

                // 优化性能
                this.optimizePerformance();

                await this.loadSettings();
                this.setupMessageListener();
                this.detectPageType();

                // 等待页面完全加载
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => this.initializeFeatures());
                } else {
                    this.initializeFeatures();
                }

                // 监听页面变化
                this.observePageChanges();

                this.isInitialized = true;
                console.log('[健教中心助手] 初始化完成');
            } catch (error) {
                this.handleError(error, '初始化');
            }
        }

        /**
         * 加载设置
         */
        async loadSettings() {
            try {
                const result = await chrome.storage.local.get([
                    'extensionEnabled',
                    'autoSkipVideo', 
                    'autoExam',
                    'playbackSpeed'
                ]);
                
                this.settings = { ...this.settings, ...result };
                console.log('[健教中心助手] 设置已加载:', this.settings);
            } catch (error) {
                console.error('[健教中心助手] 加载设置失败:', error);
            }
        }

        /**
         * 设置消息监听器
         */
        setupMessageListener() {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                console.log('[健教中心助手] 收到消息:', message);
                
                switch (message.action) {
                    case 'getStatus':
                        sendResponse({ 
                            status: this.getStatus(),
                            players: this.players.detected,
                            url: this.currentUrl
                        });
                        break;
                        
                    case 'toggleExtension':
                        this.settings.extensionEnabled = message.enabled;
                        this.updateUI();
                        sendResponse({ success: true });
                        break;
                        
                    case 'toggleAutoSkip':
                        this.settings.autoSkipVideo = message.enabled;
                        if (message.enabled && this.players.detected) {
                            this.skipVideo();
                        }
                        sendResponse({ success: true });
                        break;
                        
                    case 'toggleAutoExam':
                        this.settings.autoExam = message.enabled;
                        sendResponse({ success: true });
                        break;
                        
                    case 'setPlaybackSpeed':
                        this.settings.playbackSpeed = message.speed;
                        this.setVideoSpeed(message.speed);
                        sendResponse({ success: true });
                        break;
                        
                    case 'skipVideo':
                        this.skipVideo();
                        sendResponse({ success: true });
                        break;
                        
                    case 'resetSettings':
                        this.resetSettings();
                        sendResponse({ success: true });
                        break;
                        
                    default:
                        sendResponse({ error: '未知操作' });
                }
                
                return true; // 保持消息通道开放
            });
        }

        /**
         * 检测页面类型
         */
        detectPageType() {
            const url = window.location.href;
            const pathname = window.location.pathname;
            
            if (url.includes('exam.jsp')) {
                this.pageType = 'exam';
                console.log('[健教中心助手] 检测到考试页面');
            } else if (url.includes('examQuizFail.jsp')) {
                this.pageType = 'examFail';
                console.log('[健教中心助手] 检测到考试失败页面');
            } else if (pathname.includes('course') || pathname.includes('video')) {
                this.pageType = 'video';
                console.log('[健教中心助手] 检测到视频页面');
            } else {
                this.pageType = 'other';
                console.log('[健教中心助手] 检测到其他页面类型');
            }
        }

        /**
         * 初始化功能
         */
        initializeFeatures() {
            if (!this.settings.extensionEnabled) {
                console.log('[健教中心助手] 扩展已禁用');
                return;
            }

            console.log('[健教中心助手] 开始初始化功能...');
            
            // 根据页面类型初始化不同功能
            switch (this.pageType) {
                case 'video':
                    this.initVideoFeatures();
                    break;
                case 'exam':
                    this.initExamFeatures();
                    break;
                case 'examFail':
                    this.handleExamFail();
                    break;
                default:
                    this.initGeneralFeatures();
            }
            
            this.createUI();
            this.showStatusMessage('扩展已激活', 'success');
        }

        /**
         * 初始化视频功能
         */
        initVideoFeatures() {
            console.log('[健教中心助手] 初始化视频功能...');

            // 延迟检测播放器，等待页面完全加载
            setTimeout(() => {
                this.detectVideoPlayers();

                // 启动课程进度跟踪
                this.trackCourseProgress();

                if (this.settings.autoSkipVideo && this.players.detected) {
                    setTimeout(() => this.skipVideo(), 2000);
                }
            }, 1500);
        }

        /**
         * 检测视频播放器
         */
        detectVideoPlayers() {
            console.log('[健教中心助手] 开始检测视频播放器...');
            
            // 检测保利威播放器
            this.detectPolyvPlayer();
            
            // 检测HTML5视频元素
            this.detectHTML5Video();
            
            if (this.players.detected) {
                console.log('[健教中心助手] 播放器检测成功');
                this.showStatusMessage('播放器已检测', 'success');
            } else {
                console.log('[健教中心助手] 未检测到播放器');
                this.showStatusMessage('未检测到播放器', 'warning');
                
                // 继续尝试检测
                setTimeout(() => this.detectVideoPlayers(), 3000);
            }
        }

        /**
         * 检测保利威播放器
         */
        detectPolyvPlayer() {
            try {
                // 检测全局播放器对象
                if (window.player && window.player.params) {
                    this.players.polyv = window.player;
                    this.players.detected = true;
                    console.log('[健教中心助手] 检测到保利威播放器 (window.player)');
                    return;
                }
                
                if (window.cc_js_Player && window.cc_js_Player.params) {
                    this.players.polyv = window.cc_js_Player;
                    this.players.detected = true;
                    console.log('[健教中心助手] 检测到保利威播放器 (window.cc_js_Player)');
                    return;
                }
                
                // 检测播放器容器
                const playerContainers = document.querySelectorAll('.pv-video, .ccH5playerBox, [id*="player"], [class*="player"]');
                if (playerContainers.length > 0) {
                    console.log('[健教中心助手] 检测到播放器容器:', playerContainers.length);
                    // 尝试从容器中获取播放器实例
                    for (const container of playerContainers) {
                        if (container._player || container.player) {
                            this.players.polyv = container._player || container.player;
                            this.players.detected = true;
                            console.log('[健教中心助手] 从容器获取到播放器实例');
                            return;
                        }
                    }
                }
            } catch (error) {
                console.error('[健教中心助手] 检测保利威播放器时出错:', error);
            }
        }

        /**
         * 检测HTML5视频元素
         */
        detectHTML5Video() {
            try {
                const videos = document.querySelectorAll('video');
                if (videos.length > 0) {
                    this.players.html5 = videos[0]; // 使用第一个视频元素
                    this.players.detected = true;
                    console.log('[健教中心助手] 检测到HTML5视频元素:', videos.length);
                }
            } catch (error) {
                console.error('[健教中心助手] 检测HTML5视频时出错:', error);
            }
        }

        /**
         * 获取状态信息
         */
        getStatus() {
            if (!this.settings.extensionEnabled) {
                return '扩展已禁用';
            }

            if (this.players.detected) {
                return '播放器已检测';
            }

            return '正在检测播放器';
        }

        /**
         * 跳过视频
         */
        skipVideo() {
            console.log('[健教中心助手] 开始跳过视频...');

            if (!this.players.detected) {
                this.showStatusMessage('未检测到播放器', 'error');
                return;
            }

            try {
                // 尝试使用保利威播放器API
                if (this.players.polyv) {
                    this.skipWithPolyvPlayer();
                }

                // 尝试使用HTML5视频元素
                if (this.players.html5) {
                    this.skipWithHTML5Video();
                }

                // 查找并点击完成按钮
                setTimeout(() => this.findAndClickCompleteButton(), 1000);

                this.showStatusMessage('正在跳过视频...', 'success');
            } catch (error) {
                console.error('[健教中心助手] 跳过视频失败:', error);
                this.showStatusMessage('跳过视频失败', 'error');
            }
        }

        /**
         * 使用保利威播放器跳过视频
         */
        skipWithPolyvPlayer() {
            try {
                const player = this.players.polyv;

                // 设置音量为0
                if (player.setVolume) {
                    player.setVolume(0);
                }

                // 开始播放
                if (player.play) {
                    player.play();
                }

                // 跳转到结尾
                if (player.jumpToTime && player.getDuration) {
                    const duration = player.getDuration();
                    if (duration > 0) {
                        player.jumpToTime(duration - 0.5);
                        console.log('[健教中心助手] 使用保利威API跳转到视频结尾');
                    }
                }

                // 触发结束事件
                setTimeout(() => {
                    if (player.dispatchEvent) {
                        player.dispatchEvent(new Event('ended'));
                    }
                }, 500);

            } catch (error) {
                console.error('[健教中心助手] 保利威播放器跳过失败:', error);
            }
        }

        /**
         * 使用HTML5视频元素跳过视频
         */
        skipWithHTML5Video() {
            try {
                const video = this.players.html5;

                // 设置视频属性
                video.volume = 0;
                video.playbackRate = parseFloat(this.settings.playbackSpeed) || 2;

                // 确保视频开始播放
                const playPromise = video.play();
                if (playPromise) {
                    playPromise.catch(e => console.log('[健教中心助手] 视频播放失败:', e));
                }

                // 跳转到结尾
                if (video.duration && !isNaN(video.duration)) {
                    video.currentTime = video.duration - 0.1;
                    console.log('[健教中心助手] 使用HTML5 API跳转到视频结尾');
                } else {
                    // 监听元数据加载完成
                    video.addEventListener('loadedmetadata', () => {
                        video.currentTime = video.duration - 0.1;
                        console.log('[健教中心助手] 元数据加载后跳转到视频结尾');
                    });
                }

                // 触发结束事件
                setTimeout(() => {
                    video.dispatchEvent(new Event('ended'));
                }, 1000);

            } catch (error) {
                console.error('[健教中心助手] HTML5视频跳过失败:', error);
            }
        }

        /**
         * 查找并点击完成按钮
         */
        findAndClickCompleteButton() {
            const buttonTexts = [
                '完成学习', '学习完成', '完成视频', '视频完成',
                '下一步', '进入考试', '开始考试', '完成', '继续',
                'next', 'complete', 'finish'
            ];

            const buttons = Array.from(document.querySelectorAll('button, a, div, span'))
                .filter(el => {
                    const text = (el.textContent || '').toLowerCase();
                    return buttonTexts.some(btnText => text.includes(btnText.toLowerCase()));
                });

            if (buttons.length > 0) {
                console.log('[健教中心助手] 找到完成按钮，点击:', buttons[0].textContent);
                buttons[0].click();
                this.showStatusMessage('已点击完成按钮', 'success');
                return true;
            }

            console.log('[健教中心助手] 未找到完成按钮');
            return false;
        }

        /**
         * 设置视频播放速度
         */
        setVideoSpeed(speed) {
            const speedValue = parseFloat(speed);

            try {
                // 设置保利威播放器速度
                if (this.players.polyv && this.players.polyv.setRate) {
                    this.players.polyv.setRate(speedValue);
                    console.log('[健教中心助手] 保利威播放器速度设置为:', speedValue);
                }

                // 设置HTML5视频速度
                if (this.players.html5) {
                    this.players.html5.playbackRate = speedValue;
                    console.log('[健教中心助手] HTML5视频速度设置为:', speedValue);
                }

                this.showStatusMessage(`播放速度: ${speedValue}x`, 'success');
            } catch (error) {
                console.error('[健教中心助手] 设置播放速度失败:', error);
                this.showStatusMessage('设置速度失败', 'error');
            }
        }
    }

        /**
         * 初始化考试功能
         */
        initExamFeatures() {
            console.log('[健教中心助手] 初始化考试功能...');

            if (this.settings.autoExam) {
                setTimeout(() => this.handleExam(), 1000);
            }
        }

        /**
         * 处理考试
         */
        handleExam() {
            console.log('[健教中心助手] 开始处理考试...');

            try {
                const questions = this.getExamQuestions();
                if (questions.length > 0) {
                    this.autoAnswerQuestions(questions);
                    this.showStatusMessage('正在自动答题...', 'success');
                } else {
                    this.showStatusMessage('未找到考试题目', 'warning');
                }
            } catch (error) {
                console.error('[健教中心助手] 处理考试失败:', error);
                this.showStatusMessage('考试处理失败', 'error');
            }
        }

        /**
         * 获取考试题目
         */
        getExamQuestions() {
            const questions = [];

            // 检测不同的题目容器格式
            const questionSelectors = [
                '.kaoshi dl',           // 北京石景山样式
                '.exam_list li',        // 通用考试样式
                '.question-item',       // 其他可能的样式
                '[class*="question"]'   // 包含question的类名
            ];

            for (const selector of questionSelectors) {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    console.log(`[健教中心助手] 找到 ${elements.length} 道题目 (${selector})`);
                    return Array.from(elements);
                }
            }

            return questions;
        }

        /**
         * 自动答题
         */
        autoAnswerQuestions(questions) {
            console.log('[健教中心助手] 开始自动答题，题目数量:', questions.length);

            questions.forEach((question, index) => {
                try {
                    this.answerSingleQuestion(question, index);
                } catch (error) {
                    console.error(`[健教中心助手] 第${index + 1}题答题失败:`, error);
                }
            });

            // 提交答案
            setTimeout(() => this.submitExam(), 1000);
        }

        /**
         * 回答单个问题
         */
        answerSingleQuestion(questionElement, index) {
            const options = questionElement.querySelectorAll('input[type="radio"], input[type="checkbox"]');

            if (options.length === 0) {
                console.log(`[健教中心助手] 第${index + 1}题没有找到选项`);
                return;
            }

            // 判断题目类型
            const isMultipleChoice = options[0].type === 'checkbox';

            if (isMultipleChoice) {
                // 多选题：选择前几个选项
                const selectCount = Math.min(2, options.length);
                for (let i = 0; i < selectCount; i++) {
                    options[i].click();
                }
                console.log(`[健教中心助手] 第${index + 1}题(多选)已选择 ${selectCount} 个选项`);
            } else {
                // 单选题：选择第一个选项
                options[0].click();
                console.log(`[健教中心助手] 第${index + 1}题(单选)已选择第一个选项`);
            }
        }

        /**
         * 提交考试
         */
        submitExam() {
            const submitSelectors = [
                '#tjkj',                // 通用提交按钮ID
                '.btn1',               // 按钮样式类
                'button[type="submit"]', // 提交按钮
                'input[type="submit"]',  // 提交输入框
                '[class*="submit"]',     // 包含submit的类名
                '[id*="submit"]'         // 包含submit的ID
            ];

            for (const selector of submitSelectors) {
                const submitBtn = document.querySelector(selector);
                if (submitBtn) {
                    console.log('[健教中心助手] 找到提交按钮，准备提交');

                    // 移除target属性以避免新窗口打开
                    const form = submitBtn.closest('form');
                    if (form) {
                        form.removeAttribute('target');
                    }

                    submitBtn.click();
                    this.showStatusMessage('考试已提交', 'success');
                    return;
                }
            }

            console.log('[健教中心助手] 未找到提交按钮');
            this.showStatusMessage('未找到提交按钮', 'warning');
        }

        /**
         * 处理考试失败页面
         */
        handleExamFail() {
            console.log('[健教中心助手] 处理考试失败页面...');
            // 这里可以添加重试逻辑
            this.showStatusMessage('检测到考试失败页面', 'warning');
        }

        /**
         * 初始化通用功能
         */
        initGeneralFeatures() {
            console.log('[健教中心助手] 初始化通用功能...');
            // 可以添加一些通用的页面增强功能
        }

        /**
         * 创建用户界面
         */
        createUI() {
            if (!this.settings.extensionEnabled) {
                return;
            }

            this.createSkipButton();
            this.createExamButton();
            this.createStatusIndicator();

            console.log('[健教中心助手] UI界面已创建');
        }

        /**
         * 创建跳过视频按钮
         */
        createSkipButton() {
            if (this.ui.skipButton) {
                this.ui.skipButton.remove();
            }

            const button = document.createElement('button');
            button.className = 'health-edu-skip-button health-edu-fade-in';
            button.textContent = '跳过视频';
            button.title = '点击跳过当前视频';

            button.addEventListener('click', () => {
                this.skipVideo();
            });

            document.body.appendChild(button);
            this.ui.skipButton = button;
        }

        /**
         * 创建考试按钮
         */
        createExamButton() {
            if (this.pageType !== 'exam') {
                return;
            }

            if (this.ui.examButton) {
                this.ui.examButton.remove();
            }

            const button = document.createElement('button');
            button.className = 'health-edu-exam-button health-edu-fade-in';
            button.textContent = '自动答题';
            button.title = '点击开始自动答题';

            button.addEventListener('click', () => {
                this.handleExam();
            });

            document.body.appendChild(button);
            this.ui.examButton = button;
        }

        /**
         * 创建状态指示器
         */
        createStatusIndicator() {
            if (this.ui.statusIndicator) {
                this.ui.statusIndicator.remove();
            }

            const indicator = document.createElement('div');
            indicator.className = 'health-edu-status-indicator health-edu-fade-in';
            indicator.textContent = '健教中心助手已激活';

            document.body.appendChild(indicator);
            this.ui.statusIndicator = indicator;

            // 3秒后隐藏
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.style.opacity = '0';
                    setTimeout(() => indicator.remove(), 300);
                }
            }, 3000);
        }

        /**
         * 显示状态消息
         */
        showStatusMessage(message, type = 'info') {
            console.log(`[健教中心助手] ${message}`);

            // 创建临时状态指示器
            const indicator = document.createElement('div');
            indicator.className = `health-edu-status-indicator ${type} health-edu-fade-in`;
            indicator.textContent = message;

            document.body.appendChild(indicator);

            // 3秒后移除
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.style.opacity = '0';
                    setTimeout(() => indicator.remove(), 300);
                }
            }, 3000);
        }

        /**
         * 更新UI状态
         */
        updateUI() {
            if (this.settings.extensionEnabled) {
                this.createUI();
            } else {
                this.removeUI();
            }
        }

        /**
         * 移除UI元素
         */
        removeUI() {
            if (this.ui.skipButton) {
                this.ui.skipButton.remove();
                this.ui.skipButton = null;
            }

            if (this.ui.examButton) {
                this.ui.examButton.remove();
                this.ui.examButton = null;
            }

            if (this.ui.statusIndicator) {
                this.ui.statusIndicator.remove();
                this.ui.statusIndicator = null;
            }
        }

        /**
         * 监听页面变化
         */
        observePageChanges() {
            // 监听URL变化
            let currentUrl = window.location.href;
            const checkUrlChange = () => {
                if (window.location.href !== currentUrl) {
                    currentUrl = window.location.href;
                    this.currentUrl = currentUrl;
                    console.log('[健教中心助手] 页面URL已变化:', currentUrl);

                    // 重新检测页面类型和初始化功能
                    this.detectPageType();
                    setTimeout(() => this.initializeFeatures(), 1000);
                }
            };

            // 每秒检查一次URL变化
            setInterval(checkUrlChange, 1000);

            // 监听DOM变化
            const observer = new MutationObserver((mutations) => {
                let shouldRedetect = false;

                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有新的视频元素添加
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.tagName === 'VIDEO' ||
                                    node.querySelector && node.querySelector('video')) {
                                    shouldRedetect = true;
                                    break;
                                }
                            }
                        }
                    }
                });

                if (shouldRedetect && !this.players.detected) {
                    console.log('[健教中心助手] 检测到新的视频元素，重新检测播放器');
                    setTimeout(() => this.detectVideoPlayers(), 500);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        /**
         * 重置设置
         */
        resetSettings() {
            this.settings = {
                extensionEnabled: true,
                autoSkipVideo: false,
                autoExam: false,
                playbackSpeed: '2'
            };

            this.updateUI();
            this.showStatusMessage('设置已重置', 'success');
        }

        /**
         * 跟踪课程进度
         */
        trackCourseProgress() {
            console.log('[健教中心助手] 开始跟踪课程进度...');

            // 监听视频播放事件
            this.monitorVideoProgress();

            // 处理课程完成确认
            this.handleCourseCompletion();

            // 检查并处理互动内容
            this.handleInteractiveContent();
        }

        /**
         * 监听视频播放进度
         */
        monitorVideoProgress() {
            if (this.players.html5) {
                const video = this.players.html5;

                // 监听播放进度
                video.addEventListener('timeupdate', () => {
                    const progress = (video.currentTime / video.duration) * 100;
                    if (progress > 90) { // 播放超过90%认为已观看
                        this.markVideoAsWatched();
                    }
                });

                // 监听播放结束
                video.addEventListener('ended', () => {
                    console.log('[健教中心助手] 视频播放结束，标记为已完成');
                    this.markVideoAsCompleted();
                });
            }

            // 对于保利威播放器，尝试监听其事件
            if (this.players.polyv) {
                try {
                    const player = this.players.polyv;

                    // 监听播放结束事件
                    if (player.on) {
                        player.on('ended', () => {
                            console.log('[健教中心助手] 保利威播放器播放结束');
                            this.markVideoAsCompleted();
                        });
                    }
                } catch (error) {
                    console.error('[健教中心助手] 监听保利威播放器事件失败:', error);
                }
            }
        }

        /**
         * 标记视频为已观看
         */
        markVideoAsWatched() {
            // 触发页面的进度更新机制
            this.triggerProgressUpdate();
        }

        /**
         * 标记视频为已完成
         */
        markVideoAsCompleted() {
            console.log('[健教中心助手] 标记视频为已完成');

            // 确保触发页面的完成事件
            this.triggerProgressUpdate();

            // 查找并点击确认按钮
            setTimeout(() => {
                this.findAndClickCompleteButton();
            }, 1000);
        }

        /**
         * 触发进度更新
         */
        triggerProgressUpdate() {
            // 尝试触发页面的进度更新机制
            const progressEvents = ['progress', 'timeupdate', 'ended', 'loadeddata'];

            progressEvents.forEach(eventType => {
                if (this.players.html5) {
                    this.players.html5.dispatchEvent(new Event(eventType));
                }
            });

            // 触发窗口事件
            window.dispatchEvent(new Event('videoProgress'));
            window.dispatchEvent(new Event('courseProgress'));
        }

        /**
         * 处理课程完成确认
         */
        handleCourseCompletion() {
            // 监听可能的完成确认对话框
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // 检查是否是确认对话框
                                if (this.isCompletionDialog(node)) {
                                    this.handleCompletionDialog(node);
                                }
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        /**
         * 判断是否是完成确认对话框
         */
        isCompletionDialog(element) {
            const text = element.textContent || '';
            const completionKeywords = [
                '完成', '确认', '继续', '下一步', '进入考试',
                'complete', 'confirm', 'continue', 'next'
            ];

            return completionKeywords.some(keyword =>
                text.toLowerCase().includes(keyword.toLowerCase())
            );
        }

        /**
         * 处理完成确认对话框
         */
        handleCompletionDialog(dialog) {
            console.log('[健教中心助手] 检测到完成确认对话框');

            // 查找确认按钮
            const confirmButtons = dialog.querySelectorAll('button, a, [role="button"]');
            const confirmButton = Array.from(confirmButtons).find(btn => {
                const text = (btn.textContent || '').toLowerCase();
                return text.includes('确认') || text.includes('继续') ||
                       text.includes('confirm') || text.includes('ok');
            });

            if (confirmButton) {
                console.log('[健教中心助手] 自动点击确认按钮');
                setTimeout(() => confirmButton.click(), 500);
            }
        }

        /**
         * 处理互动内容
         */
        handleInteractiveContent() {
            // 检查页面中的互动元素
            const interactiveElements = document.querySelectorAll(
                '[class*="interactive"], [class*="quiz"], [class*="question"], ' +
                '[id*="interactive"], [id*="quiz"], [id*="question"]'
            );

            if (interactiveElements.length > 0) {
                console.log('[健教中心助手] 检测到互动内容:', interactiveElements.length);
                this.processInteractiveElements(interactiveElements);
            }
        }

        /**
         * 处理互动元素
         */
        processInteractiveElements(elements) {
            elements.forEach((element, index) => {
                setTimeout(() => {
                    this.processSingleInteractiveElement(element, index);
                }, index * 1000); // 每个元素间隔1秒处理
            });
        }

        /**
         * 处理单个互动元素
         */
        processSingleInteractiveElement(element, index) {
            console.log(`[健教中心助手] 处理第${index + 1}个互动元素`);

            // 查找可点击的选项
            const options = element.querySelectorAll('input, button, [role="button"]');

            if (options.length > 0) {
                // 简单策略：点击第一个选项
                options[0].click();
                console.log(`[健教中心助手] 已点击第${index + 1}个互动元素的第一个选项`);
            }
        }

        /**
         * 确保满足最低观看时长
         */
        ensureMinimumWatchTime(minSeconds = 30) {
            console.log(`[健教中心助手] 确保最低观看时长: ${minSeconds}秒`);

            const startTime = Date.now();

            const checkWatchTime = () => {
                const elapsed = (Date.now() - startTime) / 1000;

                if (elapsed < minSeconds) {
                    console.log(`[健教中心助手] 当前观看时长: ${elapsed.toFixed(1)}秒，继续等待...`);
                    setTimeout(checkWatchTime, 1000);
                } else {
                    console.log(`[健教中心助手] 已满足最低观看时长要求`);
                    this.markVideoAsCompleted();
                }
            };

            checkWatchTime();
        }

        /**
         * 错误处理器
         */
        handleError(error, context = '未知') {
            console.error(`[健教中心助手] ${context}发生错误:`, error);

            // 记录错误信息
            this.logError(error, context);

            // 显示用户友好的错误消息
            this.showUserFriendlyError(error, context);

            // 尝试恢复功能
            this.attemptRecovery(context);
        }

        /**
         * 记录错误信息
         */
        logError(error, context) {
            const errorInfo = {
                timestamp: new Date().toISOString(),
                context: context,
                message: error.message,
                stack: error.stack,
                url: window.location.href,
                userAgent: navigator.userAgent
            };

            // 保存到本地存储（用于调试）
            try {
                const errors = JSON.parse(localStorage.getItem('healthEduAssistant_errors') || '[]');
                errors.push(errorInfo);

                // 只保留最近的10个错误
                if (errors.length > 10) {
                    errors.splice(0, errors.length - 10);
                }

                localStorage.setItem('healthEduAssistant_errors', JSON.stringify(errors));
            } catch (e) {
                console.error('[健教中心助手] 保存错误日志失败:', e);
            }
        }

        /**
         * 显示用户友好的错误消息
         */
        showUserFriendlyError(error, context) {
            let message = '操作失败';

            switch (context) {
                case '播放器检测':
                    message = '播放器检测失败，请刷新页面重试';
                    break;
                case '视频跳过':
                    message = '视频跳过失败，可能网站不支持此功能';
                    break;
                case '自动答题':
                    message = '自动答题失败，请手动完成';
                    break;
                case '设置保存':
                    message = '设置保存失败，请重新配置';
                    break;
                default:
                    message = `${context}操作失败，请重试`;
            }

            this.showStatusMessage(message, 'error');
        }

        /**
         * 尝试恢复功能
         */
        attemptRecovery(context) {
            console.log(`[健教中心助手] 尝试从${context}错误中恢复...`);

            switch (context) {
                case '播放器检测':
                    // 重新尝试检测播放器
                    setTimeout(() => {
                        this.detectVideoPlayers();
                    }, 3000);
                    break;

                case '视频跳过':
                    // 尝试使用备用方法
                    setTimeout(() => {
                        this.findAndClickCompleteButton();
                    }, 2000);
                    break;

                case 'UI创建':
                    // 重新创建UI
                    setTimeout(() => {
                        this.createUI();
                    }, 1000);
                    break;

                default:
                    // 通用恢复：重新初始化
                    setTimeout(() => {
                        this.initializeFeatures();
                    }, 5000);
            }
        }

        /**
         * 性能监控
         */
        monitorPerformance() {
            // 监控内存使用
            if (performance.memory) {
                const memoryInfo = {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };

                console.log('[健教中心助手] 内存使用情况:', memoryInfo);

                // 如果内存使用过高，清理资源
                if (memoryInfo.used > 100) { // 100MB
                    this.cleanupResources();
                }
            }
        }

        /**
         * 清理资源
         */
        cleanupResources() {
            console.log('[健教中心助手] 开始清理资源...');

            // 清理事件监听器
            if (this.players.html5) {
                const video = this.players.html5;
                const events = ['timeupdate', 'ended', 'loadedmetadata', 'error'];
                events.forEach(event => {
                    video.removeEventListener(event, () => {});
                });
            }

            // 清理定时器（如果有的话）
            // clearInterval/clearTimeout 调用

            // 清理DOM引用
            this.removeUI();

            console.log('[健教中心助手] 资源清理完成');
        }

        /**
         * 检查网站兼容性
         */
        checkSiteCompatibility() {
            const compatibility = {
                isTargetSite: window.location.hostname.includes('phc.cmechina.net'),
                hasRequiredElements: false,
                playerSupport: false,
                jsApiAvailable: false
            };

            // 检查必需的页面元素
            const requiredSelectors = ['body', 'head'];
            compatibility.hasRequiredElements = requiredSelectors.every(selector =>
                document.querySelector(selector) !== null
            );

            // 检查播放器支持
            compatibility.playerSupport = !!(
                window.player ||
                window.cc_js_Player ||
                document.querySelector('video')
            );

            // 检查JavaScript API可用性
            compatibility.jsApiAvailable = !!(
                window.chrome &&
                window.chrome.storage &&
                window.chrome.runtime
            );

            console.log('[健教中心助手] 网站兼容性检查:', compatibility);
            return compatibility;
        }

        /**
         * 优化性能
         */
        optimizePerformance() {
            // 使用防抖函数优化频繁调用
            this.detectVideoPlayers = this.debounce(this.detectVideoPlayers.bind(this), 1000);

            // 定期清理资源
            setInterval(() => {
                this.monitorPerformance();
            }, 30000); // 每30秒检查一次

            // 优化DOM查询
            this.cacheCommonElements();
        }

        /**
         * 防抖函数
         */
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        /**
         * 缓存常用DOM元素
         */
        cacheCommonElements() {
            this.cachedElements = {
                body: document.body,
                head: document.head,
                videos: document.querySelectorAll('video'),
                buttons: document.querySelectorAll('button'),
                forms: document.querySelectorAll('form')
            };
        }
    }

    // 全局错误处理
    window.addEventListener('error', (event) => {
        console.error('[健教中心助手] 全局错误:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
        console.error('[健教中心助手] 未处理的Promise拒绝:', event.reason);
    });

    // 检查是否在目标网站
    if (window.location.hostname.includes('phc.cmechina.net')) {
        // 等待页面基本加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    try {
                        new HealthEduAssistant();
                    } catch (error) {
                        console.error('[健教中心助手] 初始化失败:', error);
                    }
                }, 500);
            });
        } else {
            setTimeout(() => {
                try {
                    new HealthEduAssistant();
                } catch (error) {
                    console.error('[健教中心助手] 初始化失败:', error);
                }
            }, 500);
        }
    } else {
        console.log('[健教中心助手] 不在目标网站，扩展未激活');
    }

})();
