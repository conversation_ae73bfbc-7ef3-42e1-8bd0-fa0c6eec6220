/**
 * 健教中心助手 - Popup脚本
 * 处理弹出窗口的用户交互和与content script的通信
 */

class PopupManager {
    constructor() {
        this.elements = {};
        this.currentTab = null;
        this.init();
    }

    /**
     * 初始化popup管理器
     */
    async init() {
        this.bindElements();
        this.bindEvents();
        await this.getCurrentTab();
        await this.loadSettings();
        await this.updateStatus();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.elements = {
            currentStatus: document.getElementById('currentStatus'),
            siteCompatibility: document.getElementById('siteCompatibility'),
            extensionEnabled: document.getElementById('extensionEnabled'),
            autoSkipVideo: document.getElementById('autoSkipVideo'),
            autoExam: document.getElementById('autoExam'),
            playbackSpeed: document.getElementById('playbackSpeed'),
            skipVideoBtn: document.getElementById('skipVideoBtn'),
            resetSettingsBtn: document.getElementById('resetSettingsBtn')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 开关变化事件
        this.elements.extensionEnabled.addEventListener('change', (e) => {
            this.saveSetting('extensionEnabled', e.target.checked);
            this.sendMessageToContent({ action: 'toggleExtension', enabled: e.target.checked });
        });

        this.elements.autoSkipVideo.addEventListener('change', (e) => {
            this.saveSetting('autoSkipVideo', e.target.checked);
            this.sendMessageToContent({ action: 'toggleAutoSkip', enabled: e.target.checked });
        });

        this.elements.autoExam.addEventListener('change', (e) => {
            this.saveSetting('autoExam', e.target.checked);
            this.sendMessageToContent({ action: 'toggleAutoExam', enabled: e.target.checked });
        });

        this.elements.playbackSpeed.addEventListener('change', (e) => {
            this.saveSetting('playbackSpeed', e.target.value);
            this.sendMessageToContent({ action: 'setPlaybackSpeed', speed: e.target.value });
        });

        // 按钮点击事件
        this.elements.skipVideoBtn.addEventListener('click', () => {
            this.sendMessageToContent({ action: 'skipVideo' });
        });

        this.elements.resetSettingsBtn.addEventListener('click', () => {
            this.resetSettings();
        });
    }

    /**
     * 获取当前活动标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            const settings = await chrome.storage.local.get([
                'extensionEnabled',
                'autoSkipVideo',
                'autoExam',
                'playbackSpeed'
            ]);

            // 设置默认值
            const defaults = {
                extensionEnabled: true,
                autoSkipVideo: false,
                autoExam: false,
                playbackSpeed: '2'
            };

            // 应用设置到UI
            Object.keys(defaults).forEach(key => {
                const value = settings[key] !== undefined ? settings[key] : defaults[key];
                if (this.elements[key]) {
                    if (this.elements[key].type === 'checkbox') {
                        this.elements[key].checked = value;
                    } else {
                        this.elements[key].value = value;
                    }
                }
            });
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    /**
     * 保存设置
     */
    async saveSetting(key, value) {
        try {
            await chrome.storage.local.set({ [key]: value });
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }

    /**
     * 更新状态显示
     */
    async updateStatus() {
        if (!this.currentTab) {
            this.elements.currentStatus.textContent = '无法获取标签页信息';
            this.elements.currentStatus.className = 'status-value error';
            this.elements.siteCompatibility.textContent = '未知';
            this.elements.siteCompatibility.className = 'status-value error';
            return;
        }

        // 检查网站兼容性
        const isCompatible = this.currentTab.url && this.currentTab.url.includes('phc.cmechina.net');
        
        if (isCompatible) {
            this.elements.siteCompatibility.textContent = '兼容';
            this.elements.siteCompatibility.className = 'status-value';
            
            // 向content script查询状态
            try {
                const response = await this.sendMessageToContent({ action: 'getStatus' });
                if (response && response.status) {
                    this.elements.currentStatus.textContent = response.status;
                    this.elements.currentStatus.className = 'status-value';
                } else {
                    this.elements.currentStatus.textContent = '扩展已加载';
                    this.elements.currentStatus.className = 'status-value';
                }
            } catch (error) {
                this.elements.currentStatus.textContent = '等待页面加载';
                this.elements.currentStatus.className = 'status-value warning';
            }
        } else {
            this.elements.siteCompatibility.textContent = '不兼容';
            this.elements.siteCompatibility.className = 'status-value error';
            this.elements.currentStatus.textContent = '请访问目标网站';
            this.elements.currentStatus.className = 'status-value error';
            
            // 禁用控制按钮
            this.elements.skipVideoBtn.disabled = true;
        }
    }

    /**
     * 向content script发送消息
     */
    async sendMessageToContent(message) {
        if (!this.currentTab) {
            throw new Error('没有活动标签页');
        }

        try {
            const response = await chrome.tabs.sendMessage(this.currentTab.id, message);
            return response;
        } catch (error) {
            console.error('发送消息到content script失败:', error);
            throw error;
        }
    }

    /**
     * 重置所有设置
     */
    async resetSettings() {
        try {
            await chrome.storage.local.clear();
            await this.loadSettings();
            
            // 通知content script重置
            this.sendMessageToContent({ action: 'resetSettings' }).catch(() => {
                // 忽略错误，可能页面还没加载content script
            });
            
            alert('设置已重置');
        } catch (error) {
            console.error('重置设置失败:', error);
            alert('重置设置失败');
        }
    }
}

// 当popup加载完成时初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
