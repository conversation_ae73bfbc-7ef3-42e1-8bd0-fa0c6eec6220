<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- 播放按钮图标 -->
  <polygon points="45,35 45,93 85,64" fill="#fff" opacity="0.9"/>
  
  <!-- 快进符号 -->
  <polygon points="75,45 75,83 95,64" fill="#fff" opacity="0.7"/>
  
  <!-- 文字 "助手" -->
  <text x="64" y="110" font-family="Microsoft YaHei, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">助手</text>
</svg>
