/**
 * 健教中心助手 - Popup样式文件
 * 为弹出窗口提供现代化的用户界面样式
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 500px;
}

.container {
    width: 320px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin: 10px;
}

.header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.header h1 {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.version {
    font-size: 12px;
    color: #7f8c8d;
    background: #ecf0f1;
    padding: 2px 8px;
    border-radius: 10px;
    display: inline-block;
}

.status-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #495057;
}

.status-value {
    font-weight: 600;
    color: #28a745;
}

.status-value.error {
    color: #dc3545;
}

.status-value.warning {
    color: #ffc107;
}

.controls-section {
    margin-bottom: 20px;
}

.control-group {
    margin-bottom: 15px;
}

.switch-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.switch-label {
    font-weight: 500;
    color: #495057;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #3087d9;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.speed-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.speed-label {
    font-weight: 500;
    color: #495057;
}

.speed-select {
    padding: 5px 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background: white;
    color: #495057;
    font-weight: 500;
    cursor: pointer;
    transition: border-color 0.3s;
}

.speed-select:focus {
    outline: none;
    border-color: #3087d9;
}

.actions-section {
    margin-bottom: 20px;
}

.action-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 10px;
}

.action-btn:last-child {
    margin-bottom: 0;
}

.action-btn.primary {
    background: linear-gradient(135deg, #3087d9, #2c5aa0);
    color: white;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #2c5aa0, #1e3d6f);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(48, 135, 217, 0.3);
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.action-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.info-section {
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.info-text {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.info-text p {
    margin-bottom: 5px;
}

.info-text p:last-child {
    margin-bottom: 0;
}
