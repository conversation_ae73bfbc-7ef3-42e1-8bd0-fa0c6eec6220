/**
 * 健教中心助手 - Content样式文件
 * 为注入到页面中的控制元素提供样式
 */

/* 扩展控制面板样式 */
.health-edu-assistant-panel {
    position: fixed !important;
    top: 10px !important;
    right: 10px !important;
    z-index: 999999 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 15px !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif !important;
    font-size: 14px !important;
    min-width: 280px !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.health-edu-assistant-panel * {
    box-sizing: border-box !important;
}

.health-edu-assistant-panel h3 {
    margin: 0 0 15px 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    text-align: center !important;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.health-edu-assistant-controls {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

.health-edu-assistant-control-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 8px 0 !important;
}

.health-edu-assistant-control-label {
    color: white !important;
    font-weight: 500 !important;
    font-size: 13px !important;
}

.health-edu-assistant-button {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(5px) !important;
}

.health-edu-assistant-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.health-edu-assistant-button:active {
    transform: translateY(0) !important;
}

.health-edu-assistant-button.primary {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    border: none !important;
    font-weight: 600 !important;
}

.health-edu-assistant-button.primary:hover {
    background: linear-gradient(135deg, #ee5a24, #d63031) !important;
}

.health-edu-assistant-select {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #333 !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    padding: 5px 8px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    cursor: pointer !important;
}

.health-edu-assistant-checkbox {
    width: 16px !important;
    height: 16px !important;
    cursor: pointer !important;
}

/* 主要跳过按钮样式 */
.health-edu-skip-button {
    position: fixed !important;
    top: 60px !important;
    right: 10px !important;
    z-index: 999998 !important;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    box-shadow: 0 4px 16px rgba(238, 90, 36, 0.4) !important;
    transition: all 0.3s ease !important;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.health-edu-skip-button:hover {
    background: linear-gradient(135deg, #ee5a24, #d63031) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(238, 90, 36, 0.5) !important;
}

.health-edu-skip-button:active {
    transform: translateY(0) !important;
}

/* 考试跳过按钮样式 */
.health-edu-exam-button {
    position: fixed !important;
    top: 120px !important;
    right: 10px !important;
    z-index: 999998 !important;
    background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    box-shadow: 0 4px 16px rgba(9, 132, 227, 0.4) !important;
    transition: all 0.3s ease !important;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.health-edu-exam-button:hover {
    background: linear-gradient(135deg, #0984e3, #0056b3) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(9, 132, 227, 0.5) !important;
}

.health-edu-exam-button:active {
    transform: translateY(0) !important;
}

/* 状态指示器 */
.health-edu-status-indicator {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 999997 !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.health-edu-status-indicator.success {
    background: rgba(40, 167, 69, 0.9) !important;
}

.health-edu-status-indicator.warning {
    background: rgba(255, 193, 7, 0.9) !important;
    color: #333 !important;
}

.health-edu-status-indicator.error {
    background: rgba(220, 53, 69, 0.9) !important;
}

/* 确保扩展元素不被页面样式影响 */
.health-edu-assistant-panel,
.health-edu-skip-button,
.health-edu-exam-button,
.health-edu-status-indicator {
    all: initial !important;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif !important;
}

/* 隐藏类 */
.health-edu-hidden {
    display: none !important;
}

/* 动画效果 */
@keyframes healthEduFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.health-edu-fade-in {
    animation: healthEduFadeIn 0.3s ease-out !important;
}
