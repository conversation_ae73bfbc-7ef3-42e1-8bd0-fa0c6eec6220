
(function() {
    'use strict';

    // 添加样式函数
    function addStyle(id, tag, css, element) {
        tag = tag || 'style';
        element = element || 'body';
        let doc = document, styleDom = doc.getElementById(id);
        if (styleDom) styleDom.remove();
        let style = doc.createElement(tag);
        style.rel = 'stylesheet';
        style.id = id;
        tag === 'style' ? style.innerHTML = css : style.href = css;
        doc.getElementsByTagName(element)[0].appendChild(style);
    }

    // 加载SweetAlert2样式
    function loadSweetAlert2() {
        // 加载SweetAlert2 JS
        const swalScript = document.createElement('script');
        swalScript.src = chrome.runtime.getURL('sweetalert2.min.js');
        document.head.appendChild(swalScript);
        
        // 加载SweetAlert2 CSS
        const swalStyle = document.createElement('link');
        swalStyle.rel = 'stylesheet';
        swalStyle.href = chrome.runtime.getURL('sweetalert2.min.css');
        document.head.appendChild(swalStyle);
        
        // 添加自定义样式
        addStyle('swal-pub-style', 'style', '.swal2-container{z-index:1999;}');
    }

    // 按钮样式
    const buttonCssText = 'position: absolute;z-index: 99;top: -50px;right: 0;padding:10px;cursor:pointer;background: #3087d9;color: #fff;border-radius: 10px;box-shadow: 0px 0px 12px rgba(0, 0, 0, .12);';

    // 获取URL参数和路径
    const lastPath = getLastUrlPath();
    const examId = `${getUrlParams('course_id')}_${getUrlParams('paper_id')}`;

    function getUrlParams(name) {
        const urlSearchParams = new URLSearchParams(window.location.search);
        return urlSearchParams.get(name);
    }
    
    function getLastUrlPath() {
        const pathList = window.location.pathname.split('/');
        return pathList[pathList.length - 1];
    }

    // 消息通信
    const channelName = `NoMoreExam_${examId}`;
    
    function sendMessageToOtherTab(message) {
        const channel = new BroadcastChannel(channelName);
        channel.postMessage(message);
    }
    
    function setupMessageListener(handler) {
        const channel = new BroadcastChannel(channelName);
        channel.onmessage = (event) => {
            const receivedMessage = event.data;
            handler(receivedMessage);
        };
    }

    // 答题相关函数
    function alertAnswerFailedMsg(index) {
        alert(`全部遍历但未找到第${index}题的正确答案, 请确定是使用脚本按钮开始答题! 请关闭此页面重新开始考试`);
    }
    
    function getNextChoice(str, questionIndex) {
        const code = str.charCodeAt(0) + 1;
        if (code === 70) {
            alertAnswerFailedMsg(questionIndex + 1);
            return 'A';
        }
        return String.fromCharCode(code);
    }
    
    function getNextMultipleChoice(str, questionIndex) {
        const dic = ['ABCDE', 'BCDE', 'ACDE', 'ABDE', 'ABCE', 'ABCD', 'CDE', 'BDE', 'BCE', 'BCD', 'ADE', 'ACE', 'ACD', 'ABE', 'ABD', 'ABC', 'DE', 'CE', 'CD', 'BE', 'BD', 'BC', 'AE', 'AD', 'AC', 'AB', 'E', 'D', 'C', 'B', 'A'];
        const index = dic.indexOf(str);
        if (index === dic.length - 1) {
            alertAnswerFailedMsg(questionIndex + 1);
            return dic[0];
        }
        return dic[index + 1];
    }

    function customQuerySelector(selectors) {
        return document.querySelectorAll(selectors)[0];
    }

    // 初始化
    loadSweetAlert2();

    // 添加日志函数以便调试
    function logToConsole(message) {
        console.log(`[好医生助手] ${message}`);
    }

    logToConsole("脚本已加载，正在初始化...");

    // 在页面加载完成后执行初始化
    window.addEventListener('load', function() {
        logToConsole("页面加载完成，开始初始化按钮和功能。");
        initializeExtension();
    });

    // 如果页面已经加载完成，直接执行初始化
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        logToConsole("页面已加载，直接执行初始化。");
        initializeExtension();
    }

    function initializeExtension() {
        // 考试失败页面处理
        if (lastPath === ('examQuizFail.jsp')) {
        if (location.host === 'bjsqypx.haoyisheng.com') {
            const error_order = getUrlParams('error_order');
            sendMessageToOtherTab(error_order);
            window.close();
            return;
        }
        const nowAnswerStr = window.location.search.split('ansList=')[1].split('&')[0];
        const nowAnswerList = nowAnswerStr.split(',');

        let currentQuestionIndex = 0;
        const answersList = document.querySelectorAll('.answer_list h3');
        let finished = true;
        for (let i = 0; i < answersList.length; i++) {
            currentQuestionIndex = i;
            if (answersList[i].className.includes('cuo')) {
                finished = false;
                if (nowAnswerList[i].length === 1) {
                    nowAnswerList[i] = getNextChoice(nowAnswerList[i], currentQuestionIndex);
                } else {
                    nowAnswerList[i] = getNextMultipleChoice(nowAnswerList[i], currentQuestionIndex);
                }
                window.location.href = window.location.href.replace(nowAnswerStr, nowAnswerList.join(','));
                break;
            }
        }
        if (finished) {
            sendMessageToOtherTab(JSON.stringify(nowAnswerList));
            window.close();
        }
        return;
    }
    
        // 考试页面处理
        if (lastPath === ('exam.jsp')) {
            const isBjsqypx = location.host === 'bjsqypx.haoyisheng.com';
            const questionsList = isBjsqypx ? document.querySelectorAll('.kaoshi dl') : document.querySelectorAll('.exam_list li');
            const submitBtn = isBjsqypx ? customQuerySelector('.but_box .btn1') : customQuerySelector('#tjkj');
            const nowAnswerObjList = [];
            const autoSelectAnswer = answerArray => {
                const indexMap = {
                    'A': 0,
                    'B': 1,
                    'C': 2,
                    'D': 3,
                    'E': 4
                };
                for (let i = 0; i < questionsList.length; i++) {
                    const answer = answerArray[i];
                    const optionsList = questionsList[i].querySelectorAll('p');
                    if (questionsList[i].querySelectorAll('input[type="radio"]').length > 0) {
                        const index = indexMap[answer] || 0;
                        const answerItem = optionsList[index];
                        const input = answerItem.children[0];
                        nowAnswerObjList[i] = {
                            type: 1,
                            value: input.value
                        };
                        input.dispatchEvent(new MouseEvent('click'));
                        continue;
                    }
                    for (let j = 0; j < optionsList.length; j++) {
                        const answerItem = optionsList[j];
                        nowAnswerObjList[i] = {
                            type: 2,
                            value: answer
                        };
                        const input = answerItem.children[0];
                        if (answer.includes(input.value) && !input.checked) {
                            input.dispatchEvent(new MouseEvent('click'));
                        }
                    }
                }
            };
            const messageHandler = message => {
                autoSelectAnswer(JSON.parse(message));
                customQuerySelector('form').removeAttribute('target');
                submitBtn.dispatchEvent(new MouseEvent('click'));
            };
            const qypxMessageHandler = message => {
                const errorOrderList = message.split(',');
                errorOrderList.forEach(order => {
                    const index = parseInt(order, 10) - 1;
                    const answer = nowAnswerObjList[index].value;
                    nowAnswerObjList[index].value = nowAnswerObjList[index].type === 1 ? getNextChoice(answer) : getNextMultipleChoice(answer);
                });
                autoSelectAnswer(nowAnswerObjList.map(item => item.value));
                customQuerySelector('form').setAttribute('target', '_blank');
                submitBtn.dispatchEvent(new MouseEvent('click'));
            };

            setupMessageListener(isBjsqypx ? qypxMessageHandler : messageHandler);

            const examSkipButton = document.createElement('button');

            examSkipButton.innerText = '考试? 拿来吧你!';
            examSkipButton.id = 'exam_skip_btn';
            examSkipButton.style.cssText = buttonCssText;
            examSkipButton.style.top = '55px';
            examSkipButton.style.right = '150px';

            examSkipButton.addEventListener('click', () => {
                const answersArray = new Array(questionsList.length).fill('ABCDE');
                autoSelectAnswer(answersArray);
                customQuerySelector('form').setAttribute('target', '_blank');
                submitBtn.dispatchEvent(new MouseEvent('click'));
            });

            // 尝试找到合适的容器来添加按钮，优先选择body以确保按钮可见
            let container = customQuerySelector('body') || customQuerySelector('.content') || customQuerySelector('.main');
            if (container) {
                examSkipButton.style.position = 'fixed';
                examSkipButton.style.top = '10px';
                examSkipButton.style.right = '10px';
                examSkipButton.style.zIndex = '9999';
                examSkipButton.style.border = '2px solid red';
                examSkipButton.style.padding = '15px';
                examSkipButton.style.fontSize = '16px';
                container.appendChild(examSkipButton);
                logToConsole("考试跳过按钮已添加到页面，使用固定位置。");
            } else {
                logToConsole("未找到合适的容器来添加考试跳过按钮。");
            }

            if (localStorage.getItem('script_auto_exam') === 'true') {
                examSkipButton.dispatchEvent(new MouseEvent('click'));
            }
            return;
        }
    }
    
    // 视频跳过功能
    setTimeout(() => {
        logToConsole("开始初始化视频跳过功能。");
        let fuckingPlayer = null;

        // 添加自动跳过视频功能
        function skipToQuiz() {
            console.log('尝试跳过视频直接进入答题...');
            
            // 定期检查页面
            const checkInterval = 2000 + Math.floor(Math.random() * 1000);
            
            const intervalId = setInterval(() => {
                // 查找视频播放器
                const videoPlayers = document.querySelectorAll('video, .video-player, .vjs-tech');
                
                videoPlayers.forEach(player => {
                    if (player instanceof HTMLVideoElement) {
                        // 如果找到视频元素，尝试直接设置为结束状态
                        if (!player.hasAttribute('data-skipped-to-end')) {
                            player.setAttribute('data-skipped-to-end', 'true');
                            console.log('找到视频，尝试跳到结尾...');
                            
                            try {
                                // 保存原始的ended事件处理器
                                const originalAddEventListener = player.addEventListener;
                                
                                // 触发视频结束事件
                                player.addEventListener = function(type, listener, options) {
                                    if (type === 'ended') {
                                        // 立即调用ended事件监听器
                                        setTimeout(() => {
            logToConsole("正在检查视频元素和播放器...");
                                            listener.call(player, new Event('ended'));
                                        }, 500);
                                    }
                                    return originalAddEventListener.call(this, type, listener, options);
                                };
                                
                                // 尝试直接设置视频为结束状态
                                if (player.duration && !isNaN(player.duration)) {
                                    console.log('设置视频时间到结尾: ' + (player.duration - 0.1));
                                    player.currentTime = player.duration - 0.1;
                                    player.volume = 0;
                                    player.playbackRate = 2;
                                    // 尝试播放视频以避免播放行为异常
                                    player.play().catch(e => console.log('播放视频失败:', e));
                                } else {
                                    console.log('视频duration未就绪，监听loadedmetadata事件...');
                                    player.addEventListener('loadedmetadata', function() {
                                        console.log('视频元数据已加载，设置时间到结尾');
                                        player.currentTime = player.duration - 0.1;
                                        player.volume = 0;
                                        player.playbackRate = 2;
                                        // 尝试播放视频以避免播放行为异常
                                        player.play().catch(e => console.log('播放视频失败:', e));
                                    });
                                }
                                
                                // 尝试触发ended事件
                                setTimeout(() => {
                                    console.log('触发视频ended事件...');
                                    player.dispatchEvent(new Event('ended'));
                                }, 1000);
                            } catch (e) {
                                console.log('跳过视频时出错:', e);
                            }
                        }
                    }
                });
                
                // 尝试直接查找并点击"完成学习"或类似按钮
                const completeButtons = Array.from(document.querySelectorAll('button, a, div, span'))
                    .filter(el => {
                        const text = (el.textContent || '').toLowerCase();
                        return text.includes('完成学习') || 
                               text.includes('学习完成') || 
                               text.includes('完成视频') ||
                               text.includes('视频完成') ||
                               text.includes('下一步') ||
                               text.includes('进入考试') ||
                               text.includes('开始考试') ||
                               text.includes('完成') || 
                               text.includes('继续') ||
                               text.includes('next') ||
                               text.includes('complete');
                    });
                
                if (completeButtons.length > 0) {
                    console.log('找到完成学习按钮，点击:', completeButtons[0].textContent);
                    completeButtons[0].click();
                    clearInterval(intervalId); // 找到并点击按钮后停止检查
                }
                
            }, checkInterval);
        }

        function initPlayer() {
            const localNoticeSkip = localStorage.getItem('swal_notice_skip');
            const autoSkip = localStorage.getItem('script_auto_skip');

            let rateAllowChange = false;
            
            if (window.player && window.player.params) {
                // 尝试设置倍速播放
                try {
                    window.player.params.rate_allow_change = true;
                    rateAllowChange = true;
                } catch (e) {
                    console.log('设置倍速播放失败:', e);
                    rateAllowChange = false;
                }
                fuckingPlayer = window.player;
            } else if (window.cc_js_Player && window.cc_js_Player.params) {
                // 尝试设置倍速播放
                try {
                    window.cc_js_Player.params.rate_allow_change = true;
                    rateAllowChange = true;
                } catch (e) {
                    console.log('设置倍速播放失败:', e);
                    rateAllowChange = false;
                }
                fuckingPlayer = window.cc_js_Player;
            }

            document.querySelector = function (selectors) {
                return document.querySelectorAll(selectors)[0];
            };

            if (fuckingPlayer) {
                if (!rateAllowChange && window.Swal) {
                    // 网站不允许倍速播放，提供跳过选项
                    window.Swal.fire({
                        title: "倍速播放受限",
                        text: "网站不允许倍速播放，是否直接跳过视频进入答题？",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonText: "跳过视频",
                        cancelButtonText: "继续观看"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 用户选择跳过视频
                            skipToQuiz();
                        }
                    });
                } else if (!localNoticeSkip && window.Swal) {
                    window.Swal.fire({
                        title: "播放器获取成功",
                        text: "倍速与一键看完功能已正常!",
                        icon: "success"
                    });
                }
                localStorage.setItem('swal_notice_skip', 'true');
            } else {
                localStorage.removeItem('swal_notice_skip');
                if (window.Swal) {
                    window.Swal.fire({
                        title: "播放器获取失败",
                        text: "似乎网站未被正确兼容? 是否尝试直接跳过视频？",
                        icon: "question",
                        showCancelButton: true,
                        confirmButtonText: "跳过视频",
                        cancelButtonText: "继续观看"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 用户选择跳过视频
                            skipToQuiz();
                        }
                    });
                }
            }
        }

        if (customQuerySelector('.main')) {
            customQuerySelector('.main').style.marginTop = '40px';
        }
        
        initPlayer();

        const video = customQuerySelector('.pv-video') || customQuerySelector('video');
        if (!video) return;
        
        const parent = video.parentElement;
        const videoSkipButton = document.createElement('button');
        const selecterLabel = document.createElement('label');
        const playRateSelecter = document.createElement('select');
        const playRateCheckbox = document.createElement('input');
        const checkboxContainer = document.createElement('div');
        const videoCheckboxLabel = document.createElement('label');
        const videoCheckbox = document.createElement('input');
        const examCheckboxLabel = document.createElement('label');
        const examCheckbox = document.createElement('input');

        const containerCssText = 'position: absolute;height: 37px;line-height: 37px;top: -50px;right: 140px;';
        const labelCssText = 'vertical-align: middle;margin-right: 5px;line-height: 37px;color: #3087d9;font-size: 15px;';
        const controllerCssText = 'vertical-align: middle;cursor: pointer; margin-right: 5px;';

        checkboxContainer.style.cssText = containerCssText;
        // 跳过按钮
        videoSkipButton.innerText = '看视频? 拿来吧你!';
        videoSkipButton.style.cssText = buttonCssText;
        // 自动看完
        videoCheckboxLabel.innerText = '自动看完:';
        videoCheckboxLabel.style.cssText = labelCssText;
        videoCheckbox.type = 'checkbox';
        videoCheckbox.style.cssText = controllerCssText;
        // 自动开考
        examCheckboxLabel.innerText = '进入考试后自动开考:';
        examCheckboxLabel.style.cssText = labelCssText;
        examCheckbox.type = 'checkbox';
        examCheckbox.style.cssText = controllerCssText;
        // 倍速
        selecterLabel.innerText = '倍速:';
        selecterLabel.style.cssText = labelCssText;
        playRateSelecter.style.cssText = controllerCssText;
        playRateSelecter.style.border = '1px solid #000';
        playRateCheckbox.type = 'checkbox';
        playRateCheckbox.style.cssText = controllerCssText;
        // 倍速选择器初始化选项
        for (let i = 1; i <= 15; i++) {
            const option = document.createElement('option');
            option.value = i;
            option.label = i;
            playRateSelecter.appendChild(option);
        }

        playRateSelecter.addEventListener('change', () => {
            localStorage.setItem('play_back_rate', playRateSelecter.value);
            if (palyRateEnable) {
                video.playbackRate = parseInt(playRateSelecter.value);
            }
        });
        playRateCheckbox.addEventListener('change', e => {
            const value = e.target.checked;
            localStorage.setItem('play_back_rate_enable', JSON.stringify(value));
            if (value) {
                video.playbackRate = parseInt(playRateSelecter.value);
            } else {
                video.playbackRate = 1;
            }
        });
        videoCheckbox.addEventListener('change', e => {
            const autoValue = e.target.checked;
            localStorage.setItem('script_auto_skip', JSON.stringify(autoValue));
        });
        examCheckbox.addEventListener('change', e => {
            const autoValue = e.target.checked;
            localStorage.setItem('script_auto_exam', JSON.stringify(autoValue));
        });
        videoSkipButton.addEventListener('click', () => {
            try {
                console.log('尝试跳过视频...');
                
                if (fuckingPlayer) {
                    console.log('使用播放器API跳过视频...');
                    try {
                        fuckingPlayer.setVolume(0);
                        fuckingPlayer.play();
                        fuckingPlayer.jumpToTime(fuckingPlayer.getDuration() - 0.5);
                        
                        // 触发视频结束事件
                        setTimeout(() => {
                            try {
                                console.log('触发播放器ended事件...');
                                fuckingPlayer.dispatchEvent(new Event('ended'));
                            } catch (e) {
                                console.log('触发播放器ended事件失败:', e);
                                // 如果触发事件失败，尝试直接查找并点击完成按钮
                                findAndClickButton();
                            }
                        }, 500);
                    } catch (e) {
                        console.log('使用播放器API跳过视频失败:', e);
                        // 如果播放器API调用失败，尝试使用skipToQuiz函数
                        console.log('尝试使用skipToQuiz函数...');
                        skipToQuiz();
                    }
                } else if (video) {
                    console.log('使用视频元素跳过视频...');
                    
                    // 防止重复处理
                    if (!video.hasAttribute('data-skipped-to-end')) {
                        video.setAttribute('data-skipped-to-end', 'true');
                        
                        try {
                            // 保存原始的ended事件处理器
                            const originalAddEventListener = video.addEventListener;
                            
                            // 重写addEventListener以立即触发ended事件
                            video.addEventListener = function(type, listener, options) {
                                if (type === 'ended') {
                                    console.log('拦截ended事件监听器，准备立即触发...');
                                    // 立即调用ended事件监听器
                                    setTimeout(() => {
                                        listener.call(video, new Event('ended'));
                                    }, 500);
                                }
                                return originalAddEventListener.call(this, type, listener, options);
                            };
                            
                            // 设置视频属性
                            video.volume = 0;
                            try {
                                video.playbackRate = parseInt(playRateSelecter.value) || 1;
                            } catch (e) {
                                console.log('设置倍速失败，可能网站不允许倍速播放:', e);
                                // 倍速设置失败不影响跳过功能
                            }
                            
                            // 确保duration已加载
                            if (video.duration && !isNaN(video.duration)) {
                                console.log('设置视频时间到结尾: ' + (video.duration - 0.1));
                                video.currentTime = video.duration - 0.1;
                            } else {
                                console.log('视频duration未就绪，监听loadedmetadata事件...');
                                video.addEventListener('loadedmetadata', function() {
                                    console.log('视频元数据已加载，设置时间到结尾: ' + (video.duration - 0.1));
                                    video.currentTime = video.duration - 0.1;
                                });
                            }
                            
                            // 尝试触发ended事件
                            console.log('尝试触发视频ended事件...');
                            video.dispatchEvent(new Event('ended'));
                        } catch (e) {
                            console.log('处理视频元素时出错:', e);
                            // 如果处理视频元素失败，尝试使用skipToQuiz函数
                            skipToQuiz();
                        }
                    }
                } else {
                    console.log('未找到可用的视频播放器，尝试使用skipToQuiz函数...');
                    skipToQuiz();
                }
                
                // 尝试查找并点击"完成"或"下一步"按钮
                const checkInterval = 1000; // 1秒检查一次
                const maxAttempts = 5; // 最多尝试5次
                let attempts = 0;
                
                const findAndClickButton = () => {
                    console.log(`尝试查找完成按钮 (${attempts+1}/${maxAttempts})...`);
                    const completeButtons = Array.from(document.querySelectorAll('button, a, div, span'))
                        .filter(el => {
                            const text = (el.textContent || '').toLowerCase();
                            return text.includes('完成学习') || 
                                   text.includes('学习完成') || 
                                   text.includes('完成视频') ||
                                   text.includes('视频完成') ||
                                   text.includes('下一步') ||
                                   text.includes('进入考试') ||
                                   text.includes('开始考试') ||
                                   text.includes('完成') || 
                                   text.includes('继续') ||
                                   text.includes('next') ||
                                   text.includes('complete');
                        });
                    
                    if (completeButtons.length > 0) {
                        console.log('找到完成按钮，点击:', completeButtons[0].textContent);
                        completeButtons[0].click();
                        return true;
                    }
                    
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(findAndClickButton, checkInterval);
                    } else {
                        console.log('未能找到完成按钮，达到最大尝试次数');
                        // 如果找不到按钮，尝试使用skipToQuiz函数
                        skipToQuiz();
                    }
                    return false;
                };
                
                // 延迟一秒后开始查找按钮
                setTimeout(findAndClickButton, 1000);
            logToConsole("视频跳过功能已初始化。");
            } catch (e) {
                console.log('跳过视频时出错:', e);
                // 显示错误提示并提供使用skipToQuiz的选项
                if (window.Swal) {
                    window.Swal.fire({
                        title: "视频跳过失败",
                        text: "网站可能不允许倍速播放或跳过，是否尝试其他方式跳过？",
                        icon: "error",
                        showCancelButton: true,
                        confirmButtonText: "尝试其他方式",
                        cancelButtonText: "取消"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // 用户选择尝试其他方式
                            skipToQuiz();
                        }
                    });
                } else {
                    if (confirm("视频跳过失败，可能是网站不允许倍速播放，是否尝试其他方式跳过？")) {
                        skipToQuiz();
                    }
                }
            }
        });

        // 调整页面样式以确保按钮可见
        let contentH5 = customQuerySelector('.content .h5');
        if (contentH5) {
            contentH5.style.marginBottom = '50px';
        }
        let playerBox = customQuerySelector('.ccH5playerBox');
        if (playerBox) {
            playerBox.style.overflow = 'visible';
        }

        checkboxContainer.style.position = 'fixed';
        checkboxContainer.style.top = '60px';
        checkboxContainer.style.right = '10px';
        checkboxContainer.style.zIndex = '9999';
        videoSkipButton.style.position = 'fixed';
        videoSkipButton.style.top = '10px';
        videoSkipButton.style.right = '200px';
        videoSkipButton.style.zIndex = '9999';
        videoSkipButton.style.border = '2px solid red';
        videoSkipButton.style.padding = '15px';
        videoSkipButton.style.fontSize = '16px';

        checkboxContainer.append(examCheckboxLabel, examCheckbox, videoCheckboxLabel, videoCheckbox, selecterLabel, playRateCheckbox, playRateSelecter);
        let container = customQuerySelector('body') || customQuerySelector('.content') || customQuerySelector('.main');
        if (container) {
            container.appendChild(checkboxContainer);
            container.appendChild(videoSkipButton);
            logToConsole("视频跳过按钮和控制面板已添加到页面，使用固定位置。");
        } else {
            logToConsole("未找到合适的容器来添加视频跳过按钮和控制面板。");
        }

        if (localStorage.getItem('script_auto_skip') === 'true') {
            videoCheckbox.checked = true;
            videoSkipButton.dispatchEvent(new MouseEvent('click'));
        }
        if (localStorage.getItem('script_auto_exam') === 'true') {
            examCheckbox.checked = true;
        }

        const localRate = localStorage.getItem('play_back_rate');
        const palyRateEnable = localStorage.getItem('play_back_rate_enable');
        if (!localRate) {
            return;
        }
        let rate = parseInt(localRate);
        if (!isNaN(rate) && rate >= 1 && rate <= 15) {
            playRateSelecter.value = localRate;
        } else {
            playRateSelecter.value = '10';
            rate = 10;
        }

        if (palyRateEnable === 'true') {
            playRateCheckbox.checked = true;
            video.playbackRate = rate;
        }
    }, 1500);

    // 持续检查页面元素，确保按钮和功能能够初始化
    let checkInterval = setInterval(() => {
        if (document.body && !document.getElementById('exam_skip_btn') && !document.getElementById('video_skip_btn')) {
            logToConsole("持续检查页面元素，尝试重新初始化扩展程序。");
            initializeExtension();
        } else if (document.body) {
            clearInterval(checkInterval);
        }
    }, 2000);
})();
