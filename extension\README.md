# 健教中心助手 Chrome 扩展

## 简介

健教中心助手是一个专门为石景山教委学生体健中心云课堂平台(https://phc.cmechina.net)开发的Chrome浏览器扩展插件。该扩展可以帮助用户自动化处理视频课程和考试，提高学习效率。

## 主要功能

### 🎥 视频播放控制
- **自动检测播放器**: 支持保利威(Polyv)播放器和HTML5视频元素
- **一键跳过视频**: 快速跳转到视频结尾并触发完成事件
- **播放速度控制**: 支持1x-16x倍速播放
- **自动播放**: 可设置自动跳过所有视频

### 📝 考试自动化
- **自动答题**: 智能识别题目并自动选择答案
- **自动提交**: 完成答题后自动提交考试
- **错误重试**: 支持考试失败后的重试机制

### ⚙️ 用户控制
- **弹出窗口控制**: 点击扩展图标打开控制面板
- **实时状态显示**: 显示当前扩展状态和播放器检测情况
- **设置保存**: 自动保存用户偏好设置

### 🔒 安全特性
- **网站限制**: 仅在目标网站上激活，不影响其他网站
- **隐私保护**: 不收集任何用户个人信息
- **优雅降级**: 遇到错误时不影响网站正常功能

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器，进入扩展管理页面：
   - 地址栏输入 `chrome://extensions/`
   - 或者点击菜单 → 更多工具 → 扩展程序

2. 开启"开发者模式"（页面右上角的开关）

3. 点击"加载已解压的扩展程序"

4. 选择本扩展的文件夹（包含manifest.json的文件夹）

5. 扩展安装完成，图标会出现在浏览器工具栏

### 方法二：打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择扩展文件夹，生成.crx文件
3. 将.crx文件拖拽到扩展管理页面进行安装

## 使用方法

### 基本使用

1. **访问目标网站**: 打开 https://phc.cmechina.net
2. **登录账户**: 确保已登录到云课堂平台
3. **进入课程**: 访问包含视频的课程页面
4. **使用功能**: 扩展会自动检测页面并显示控制按钮

### 控制面板

点击浏览器工具栏中的扩展图标，打开控制面板：

- **启用扩展**: 总开关，控制扩展是否工作
- **自动跳过视频**: 开启后会自动跳过所有视频
- **自动答题**: 开启后会自动处理考试题目
- **播放速度**: 设置视频播放倍速（1x-16x）

### 页面按钮

在兼容的页面上，扩展会显示以下按钮：

- **跳过视频**: 立即跳过当前视频
- **自动答题**: 开始自动答题（仅在考试页面显示）

### 状态指示

页面右下角会显示扩展状态：
- 绿色：功能正常工作
- 黄色：警告或等待状态
- 红色：错误或功能不可用

## 技术特性

### 播放器兼容性
- 保利威(Polyv)播放器 API
- HTML5 视频元素
- 自动检测和适配不同播放器

### 智能检测
- 页面类型自动识别
- 动态内容监听
- URL变化检测

### 错误处理
- 优雅的错误恢复
- 详细的控制台日志
- 用户友好的错误提示

## 注意事项

### 使用限制
- 仅在 phc.cmechina.net 域名下工作
- 需要Chrome浏览器版本88+
- 某些页面可能需要手动刷新以激活扩展

### 免责声明
- 本扩展仅供学习和研究使用
- 用户需遵守相关网站的使用条款
- 开发者不承担因使用本扩展产生的任何责任

### 隐私政策
- 不收集任何用户个人信息
- 不向第三方发送数据
- 所有设置仅保存在本地浏览器中

## 故障排除

### 常见问题

**Q: 扩展没有激活？**
A: 确保访问的是 phc.cmechina.net 网站，并且已登录账户。

**Q: 播放器检测失败？**
A: 等待页面完全加载，或尝试刷新页面。某些播放器需要时间初始化。

**Q: 跳过视频不工作？**
A: 检查播放器是否支持，尝试手动播放视频后再使用跳过功能。

**Q: 自动答题不准确？**
A: 自动答题使用简单的选择策略，建议仅在练习时使用。

### 调试信息

开启浏览器开发者工具(F12)，查看控制台中以"[健教中心助手]"开头的日志信息。

## 更新日志

### v1.0.0 (2024-07-21)
- 初始版本发布
- 支持视频播放器检测和控制
- 支持自动跳过视频功能
- 支持考试自动答题
- 提供用户控制界面
- 实现设置保存和同步

## 开发信息

- **开发语言**: JavaScript (ES6+)
- **框架**: Chrome Extension Manifest V3
- **兼容性**: Chrome 88+
- **许可证**: MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub Repository]
- 问题反馈: [Issues Page]

---

**注意**: 请合理使用本扩展，遵守相关法律法规和网站使用条款。
