<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健教中心助手</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>健教中心助手</h1>
            <div class="version">v1.0.0</div>
        </div>
        
        <div class="status-section">
            <div class="status-item">
                <span class="status-label">当前状态:</span>
                <span id="currentStatus" class="status-value">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">网站兼容:</span>
                <span id="siteCompatibility" class="status-value">检测中...</span>
            </div>
        </div>

        <div class="controls-section">
            <div class="control-group">
                <label class="switch-container">
                    <span class="switch-label">启用扩展</span>
                    <label class="switch">
                        <input type="checkbox" id="extensionEnabled">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>

            <div class="control-group">
                <label class="switch-container">
                    <span class="switch-label">自动跳过视频</span>
                    <label class="switch">
                        <input type="checkbox" id="autoSkipVideo">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>

            <div class="control-group">
                <label class="switch-container">
                    <span class="switch-label">自动答题</span>
                    <label class="switch">
                        <input type="checkbox" id="autoExam">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>

            <div class="control-group">
                <label class="speed-container">
                    <span class="speed-label">播放速度:</span>
                    <select id="playbackSpeed" class="speed-select">
                        <option value="1">1x</option>
                        <option value="2">2x</option>
                        <option value="4">4x</option>
                        <option value="8">8x</option>
                        <option value="16">16x</option>
                    </select>
                </label>
            </div>
        </div>

        <div class="actions-section">
            <button id="skipVideoBtn" class="action-btn primary">跳过当前视频</button>
            <button id="resetSettingsBtn" class="action-btn secondary">重置设置</button>
        </div>

        <div class="info-section">
            <div class="info-text">
                <p>仅在石景山教委学生体健中心云课堂平台生效</p>
                <p>使用前请确保已登录目标网站</p>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
