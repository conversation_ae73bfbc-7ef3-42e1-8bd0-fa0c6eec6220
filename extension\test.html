<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健教中心助手 - 测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h2 {
            color: #666;
            margin-top: 0;
        }
        
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        
        video {
            width: 100%;
            max-width: 600px;
            height: 300px;
            border-radius: 8px;
        }
        
        .exam-section {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
        }
        
        .question {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
        }
        
        .question h3 {
            margin-top: 0;
            color: #333;
        }
        
        .options {
            margin-left: 20px;
        }
        
        .options label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
        }
        
        .options input {
            margin-right: 8px;
        }
        
        .submit-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .submit-btn:hover {
            background: #0056b3;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
        }
        
        .complete-btn {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .complete-btn:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>健教中心助手 - 功能测试页面</h1>
        
        <div class="test-section">
            <h2>📹 视频播放测试</h2>
            <p>此部分用于测试视频播放器检测和控制功能。</p>
            
            <div class="video-container">
                <video controls id="testVideo">
                    <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                    您的浏览器不支持视频标签。
                </video>
            </div>
            
            <div class="status">
                <strong>测试说明：</strong>
                <ul>
                    <li>扩展应该能够检测到上方的HTML5视频元素</li>
                    <li>点击"跳过视频"按钮应该能够跳转到视频结尾</li>
                    <li>播放速度控制应该能够改变视频播放速度</li>
                </ul>
            </div>
            
            <button class="complete-btn" onclick="markVideoComplete()">完成学习</button>
            <button class="complete-btn" onclick="nextVideo()">下一个视频</button>
        </div>
        
        <div class="test-section">
            <h2>📝 考试功能测试</h2>
            <p>此部分用于测试自动答题功能。</p>
            
            <div class="exam-section">
                <form id="examForm">
                    <div class="question">
                        <h3>1. 这是一道单选题测试</h3>
                        <div class="options">
                            <label><input type="radio" name="q1" value="A"> A. 选项A</label>
                            <label><input type="radio" name="q1" value="B"> B. 选项B</label>
                            <label><input type="radio" name="q1" value="C"> C. 选项C</label>
                            <label><input type="radio" name="q1" value="D"> D. 选项D</label>
                        </div>
                    </div>
                    
                    <div class="question">
                        <h3>2. 这是一道多选题测试</h3>
                        <div class="options">
                            <label><input type="checkbox" name="q2" value="A"> A. 选项A</label>
                            <label><input type="checkbox" name="q2" value="B"> B. 选项B</label>
                            <label><input type="checkbox" name="q2" value="C"> C. 选项C</label>
                            <label><input type="checkbox" name="q2" value="D"> D. 选项D</label>
                        </div>
                    </div>
                    
                    <div class="question">
                        <h3>3. 另一道单选题</h3>
                        <div class="options">
                            <label><input type="radio" name="q3" value="A"> A. 选项A</label>
                            <label><input type="radio" name="q3" value="B"> B. 选项B</label>
                            <label><input type="radio" name="q3" value="C"> C. 选项C</label>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-btn" id="tjkj">提交考试</button>
                </form>
            </div>
            
            <div class="status">
                <strong>测试说明：</strong>
                <ul>
                    <li>扩展应该能够检测到考试题目</li>
                    <li>自动答题功能应该能够自动选择答案</li>
                    <li>点击"自动答题"按钮后应该自动填写并提交</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h2>⚙️ 扩展控制测试</h2>
            <p>此部分用于测试扩展的控制功能。</p>
            
            <div class="status">
                <strong>测试步骤：</strong>
                <ol>
                    <li>点击浏览器工具栏中的扩展图标</li>
                    <li>检查弹出窗口是否正常显示</li>
                    <li>测试各种开关和设置选项</li>
                    <li>检查页面上是否出现控制按钮</li>
                    <li>测试按钮功能是否正常工作</li>
                </ol>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔍 调试信息</h2>
            <p>打开浏览器开发者工具(F12)，查看控制台中的日志信息。</p>
            
            <div class="status">
                <strong>预期日志：</strong>
                <ul>
                    <li>[健教中心助手] 开始初始化...</li>
                    <li>[健教中心助手] 检测到视频页面</li>
                    <li>[健教中心助手] 检测到HTML5视频元素</li>
                    <li>[健教中心助手] UI界面已创建</li>
                </ul>
            </div>
            
            <button class="complete-btn" onclick="showDebugInfo()">显示调试信息</button>
            <button class="complete-btn" onclick="clearLogs()">清除日志</button>
        </div>
    </div>
    
    <script>
        // 模拟页面功能
        function markVideoComplete() {
            alert('视频学习完成！');
            console.log('视频完成事件触发');
        }
        
        function nextVideo() {
            alert('进入下一个视频');
            console.log('下一个视频事件触发');
        }
        
        function showDebugInfo() {
            const info = {
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                videoElements: document.querySelectorAll('video').length,
                formElements: document.querySelectorAll('form').length
            };
            
            console.log('调试信息:', info);
            alert('调试信息已输出到控制台');
        }
        
        function clearLogs() {
            console.clear();
            alert('控制台日志已清除');
        }
        
        // 表单提交处理
        document.getElementById('examForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('考试已提交！');
            console.log('考试提交事件触发');
        });
        
        // 模拟页面加载完成
        window.addEventListener('load', function() {
            console.log('测试页面加载完成');
        });
    </script>
</body>
</html>
