# 图标文件说明

## 需要的图标文件

为了使扩展正常工作，需要创建以下PNG格式的图标文件：

- `icon16.png` - 16x16像素
- `icon32.png` - 32x32像素  
- `icon48.png` - 48x48像素
- `icon128.png` - 128x128像素

## 图标设计建议

### 设计元素
- 主色调：蓝紫色渐变 (#667eea 到 #764ba2)
- 图标：播放按钮 + 快进符号
- 风格：现代、简洁、易识别

### 设计要求
- 背景：圆形或圆角矩形
- 图标：白色或浅色
- 对比度：确保在不同背景下清晰可见
- 一致性：各尺寸保持设计一致

## 创建方法

### 方法一：使用在线工具
1. 访问 SVG to PNG 转换网站
2. 上传 `icon.svg` 文件
3. 设置输出尺寸为所需大小
4. 下载生成的PNG文件
5. 重命名为对应的文件名

### 方法二：使用图像编辑软件
1. 使用 Photoshop、GIMP 或其他图像编辑软件
2. 创建新文档，设置对应尺寸
3. 参考 `icon.svg` 设计图标
4. 导出为PNG格式

### 方法三：使用命令行工具
如果安装了 ImageMagick 或 Inkscape：

```bash
# 使用 Inkscape 转换 SVG
inkscape icon.svg -w 16 -h 16 -o icon16.png
inkscape icon.svg -w 32 -h 32 -o icon32.png
inkscape icon.svg -w 48 -h 48 -o icon48.png
inkscape icon.svg -w 128 -h 128 -o icon128.png

# 使用 ImageMagick 转换
convert icon.svg -resize 16x16 icon16.png
convert icon.svg -resize 32x32 icon32.png
convert icon.svg -resize 48x48 icon48.png
convert icon.svg -resize 128x128 icon128.png
```

## 临时解决方案

如果暂时无法创建PNG图标，可以：

1. **修改manifest.json**，移除图标引用：
```json
{
  "action": {
    "default_popup": "popup.html",
    "default_title": "健教中心助手"
  }
}
```

2. **使用默认图标**，Chrome会使用默认的扩展图标

3. **稍后添加图标**，扩展功能不受影响

## 图标用途

- **16x16**: 扩展管理页面小图标
- **32x32**: Windows任务栏图标
- **48x48**: 扩展管理页面详情图标
- **128x128**: Chrome网上应用店图标

## 注意事项

- 确保图标文件名与manifest.json中的引用一致
- PNG格式支持透明背景
- 建议使用高质量图标以确保清晰度
- 测试图标在不同背景下的显示效果
